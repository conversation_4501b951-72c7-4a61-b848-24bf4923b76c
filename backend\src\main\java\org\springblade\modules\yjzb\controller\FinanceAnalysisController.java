package org.springblade.modules.yjzb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;
import org.springblade.modules.yjzb.service.IFinanceAnalysisService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务分析控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/yjzb/finance-analysis")
@Tag(name = "财务分析", description = "财务分析接口")
public class FinanceAnalysisController extends BladeController {

    private final IFinanceAnalysisService financeAnalysisService;

    /**
     * 获取主要经济指标分析数据
     */
    @GetMapping("/main-economic-indicators")
    @Operation(summary = "获取主要经济指标分析数据", description = "获取主要经济指标分析数据，包括税利总额、利润总额、税费、卷烟销售数量、收入、成本、毛利额等")
    @Deprecated
    public R<List<Map<String, Object>>> getMainEconomicIndicators(
            @Parameter(description = "查询年份", required = true) Integer queryYear,
            @Parameter(description = "对比年份", required = false) Integer compareYear,
            @Parameter(description = "开始月份", required = true) Integer startMonth,
            @Parameter(description = "结束月份", required = true) Integer endMonth) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("queryYear", queryYear);
        params.put("compareYear", compareYear);
        params.put("startMonth", startMonth);
        params.put("endMonth", endMonth);
        
        return R.data(financeAnalysisService.selectMainEconomicIndicators(params));
    }

    /**
     * 获取三项费用分析数据
     */
    @GetMapping("/three-expenses")
    @Operation(summary = "获取三项费用分析数据", description = "获取三项费用分析数据，包括销售费用、管理费用、财务费用等明细")
    public R<List<Map<String, Object>>> getThreeExpenses(
            @Parameter(description = "查询年份", required = true) Integer queryYear,
            @Parameter(description = "对比年份", required = false) Integer compareYear,
            @Parameter(description = "开始月份", required = true) Integer startMonth,
            @Parameter(description = "结束月份", required = true) Integer endMonth) {

        Map<String, Object> params = new HashMap<>();
        params.put("queryYear", queryYear);
        params.put("compareYear", compareYear);
        params.put("startMonth", startMonth);
        params.put("endMonth", endMonth);

        return R.data(financeAnalysisService.selectThreeExpenses(params));
    }

    /**
     * 分析主要经济指标并保存结果
     */
    @GetMapping("/analyze-main-economic-indicators")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "分析主要经济指标", description = "异步分析主要经济指标并保存结果到财务分析表")
    public R<String> analyzeMainEconomicIndicators(
            @Parameter(description = "查询年份", required = true) Integer queryYear,
            @Parameter(description = "对比年份", required = false) Integer compareYear,
            @Parameter(description = "开始月份", required = true) Integer startMonth,
            @Parameter(description = "结束月份", required = true) Integer endMonth) {
        
        try {
            String workflowRunId = financeAnalysisService.analyzeMainEconomicIndicators(queryYear, compareYear, startMonth, endMonth);
            return R.data(workflowRunId, "分析任务已启动，请稍后查询结果");
        } catch (Exception e) {
            log.error("分析主要经济指标失败", e);
            return R.fail("分析失败：" + e.getMessage());
        }
    }
    
    /**
     * 分页查询财务分析数据
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询", description = "分页查询财务分析数据")
    public R<IPage<FinanceAnalysisVO>> page(FinanceAnalysisVO financeAnalysis, Query query) {
        IPage<FinanceAnalysisVO> pages = financeAnalysisService.selectFinanceAnalysisPage(Condition.getPage(query), financeAnalysis);
        return R.data(pages);
    }
    
    /**
     * 获取财务分析详情
     */
    @GetMapping("/detail")
    @Operation(summary = "详情", description = "获取财务分析详情")
    public R<FinanceAnalysisVO> detail(@Parameter(description = "主键", required = true) Long id) {
        FinanceAnalysisVO detail = financeAnalysisService.getFinanceAnalysisDetail(id);
        return R.data(detail);
    }
    
    /**
     * 根据workflowRunId获取并更新财务分析结果
     */
    @GetMapping("/result-by-workflow/{workflowRunId}")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "根据workflowRunId获取结果", description = "根据workflowRunId获取财务分析结果")
    public R<FinanceAnalysisVO> getResultByWorkflowRunId(
            @Parameter(description = "工作流运行ID", required = true) @PathVariable String workflowRunId) {
        try {
            FinanceAnalysisVO result = financeAnalysisService.updateAnalysisResultByWorkflowRunId(workflowRunId);
            if (null == result) {
                return R.fail("未找到对应的财务分析记录");
            }
            
            return R.data(result);
        } catch (Exception e) {
            log.error("根据workflowRunId获取财务分析结果失败", e);
            return R.fail("获取结果失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据组合主键查询财务分析列表
     */
    @GetMapping("/list-by-composite-key")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "根据组合主键查询列表", description = "根据name、queryYear、compareYear、startMonth、endMonth查询财务分析列表")
    public R<List<FinanceAnalysisVO>> listByCompositeKey(
            @Parameter(description = "分析名称") @RequestParam(required = false) String name,
            @Parameter(description = "查询年份") @RequestParam(required = false) Integer queryYear,
            @Parameter(description = "对比年份") @RequestParam(required = false) Integer compareYear,
            @Parameter(description = "开始月份") @RequestParam(required = false) Integer startMonth,
            @Parameter(description = "结束月份") @RequestParam(required = false) Integer endMonth) {
        
        try {
            List<FinanceAnalysisVO> list = financeAnalysisService.listByCompositeKey(name, queryYear, compareYear, startMonth, endMonth);
            return R.data(list);
        } catch (Exception e) {
            log.error("根据组合主键查询财务分析列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 新增财务分析
     */
    @PostMapping("/save")
    @Operation(summary = "新增", description = "新增财务分析")
    public R save(@Valid @RequestBody FinanceAnalysisEntity financeAnalysis) {
        return R.status(financeAnalysisService.saveFinanceAnalysis(financeAnalysis));
    }
    
    /**
     * 修改财务分析
     */
    @PostMapping("/update")
    @Operation(summary = "修改", description = "修改财务分析")
    public R update(@Valid @RequestBody FinanceAnalysisEntity financeAnalysis) {
        return R.status(financeAnalysisService.updateFinanceAnalysis(financeAnalysis));
    }
    
    /**
     * 删除财务分析
     */
    @PostMapping("/remove")
    @Operation(summary = "删除", description = "删除财务分析")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(financeAnalysisService.removeFinanceAnalysis(ids));
    }
}