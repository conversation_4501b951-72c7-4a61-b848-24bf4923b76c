/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.RequiredArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;

import org.springblade.modules.yjzb.pojo.entity.CigaretteSalesEntity;
import org.springblade.modules.yjzb.pojo.vo.CigaretteSalesVO;
import org.springblade.modules.yjzb.wrapper.CigaretteSalesWrapper;
import org.springblade.modules.yjzb.service.ICigaretteSalesService;

/**
 * 卷烟销量 控制器
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/yjzb/cigarette-sales")
@Tag(name = "卷烟销量", description = "卷烟销量接口")
public class CigaretteSalesController {

	private final ICigaretteSalesService cigaretteSalesService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@Operation(summary = "详情", description = "传入cigaretteSales")
	public R<CigaretteSalesVO> detail(CigaretteSalesEntity cigaretteSales) {
		CigaretteSalesEntity detail = cigaretteSalesService.getOne(Condition.getQueryWrapper(cigaretteSales));
		return R.data(CigaretteSalesWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 卷烟销量
	 */
	@GetMapping("/list")
	@Operation(summary = "分页", description = "传入cigaretteSales")
	@ApiOperationSupport(order = 2)
	public R<IPage<CigaretteSalesVO>> list(CigaretteSalesVO cigaretteSales, Query query) {
		IPage<CigaretteSalesVO> pages = cigaretteSalesService.selectCigaretteSalesPage(Condition.getPage(query), cigaretteSales);
		return R.data(pages);
	}

	/**
	 * 新增 卷烟销量
	 */
	@PostMapping("/save")
	@Operation(summary = "新增", description = "传入cigaretteSales")
	@ApiOperationSupport(order = 4)
	public R save(@Valid @RequestBody CigaretteSalesEntity cigaretteSales) {
		return R.status(cigaretteSalesService.save(cigaretteSales));
	}

	/**
	 * 修改 卷烟销量
	 */
	@PostMapping("/update")
	@Operation(summary = "修改", description = "传入cigaretteSales")
	@ApiOperationSupport(order = 5)
	public R update(@Valid @RequestBody CigaretteSalesEntity cigaretteSales) {
		return R.status(cigaretteSalesService.updateById(cigaretteSales));
	}

	/**
	 * 新增或修改 卷烟销量
	 */
	@PostMapping("/submit")
	@Operation(summary = "新增或修改", description = "传入cigaretteSales")
	@ApiOperationSupport(order = 6)
	public R submit(@Valid @RequestBody CigaretteSalesEntity cigaretteSales) {
		return R.status(cigaretteSalesService.saveOrUpdate(cigaretteSales));
	}

	/**
	 * 删除 卷烟销量
	 */
	@PostMapping("/remove")
	@Operation(summary = "删除", description = "传入ids")
	@ApiOperationSupport(order = 7)
	public R remove(@Parameter(description = "主键集合") @RequestParam String ids) {
		return R.status(cigaretteSalesService.removeByIds(Func.toLongList(ids)));
	}

}