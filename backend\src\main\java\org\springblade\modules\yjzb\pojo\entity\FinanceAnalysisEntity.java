package org.springblade.modules.yjzb.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.util.Date;

/**
 * 财务分析实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("yjzb_finance_analysis")
@Schema(description = "财务分析对象")
@EqualsAndHashCode(callSuper = true)
public class FinanceAnalysisEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 输入参数
     */
    @Schema(description = "输入参数")
    private String inputParams;

    /**
     * workflow_run_id
     */
    @Schema(description = "workflow_run_id")
    private String workflowRunId;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private Date executeTime;

    /**
     * 执行状态
     */
    @Schema(description = "执行状态")
    private String executeStatus;

    /**
     * 分析名称
     */
    @Schema(description = "分析名称")
    private String name;

    /**
     * 分析类型
     */
    @Schema(description = "分析类型")
    private String type;

    /**
     * 查询年份
     */
    @Schema(description = "查询年份")
    private Integer queryYear;

    /**
     * 对比年份
     */
    @Schema(description = "对比年份")
    private Integer compareYear;

    /**
     * 开始月份
     */
    @Schema(description = "开始月份")
    private Integer startMonth;

    /**
     * 结束月份
     */
    @Schema(description = "结束月份")
    private Integer endMonth;

    /**
     * 分析结果（JSON格式）
     */
    @Schema(description = "分析结果（JSON格式）")
    private String result;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}