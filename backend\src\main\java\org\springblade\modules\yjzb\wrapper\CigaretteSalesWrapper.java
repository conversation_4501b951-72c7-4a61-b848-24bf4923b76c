/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.yjzb.pojo.entity.CigaretteSalesEntity;
import org.springblade.modules.yjzb.pojo.vo.CigaretteSalesVO;
import java.util.Objects;

/**
 * 卷烟销量包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
public class CigaretteSalesWrapper extends BaseEntityWrapper<CigaretteSalesEntity, CigaretteSalesVO> {

	public static CigaretteSalesWrapper build() {
		return new CigaretteSalesWrapper();
	}

	@Override
	public CigaretteSalesVO entityVO(CigaretteSalesEntity cigaretteSales) {
		CigaretteSalesVO cigaretteSalesVO = Objects.requireNonNull(BeanUtil.copy(cigaretteSales, CigaretteSalesVO.class));
		// 此处可以处理字典值或者自定义值的转换
		return cigaretteSalesVO;
	}

}