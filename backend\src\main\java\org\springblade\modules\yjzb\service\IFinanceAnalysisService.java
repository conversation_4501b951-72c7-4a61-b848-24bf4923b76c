package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;

import java.util.List;
import java.util.Map;

/**
 * 财务分析服务接口
 *
 * <AUTHOR>
 */
public interface IFinanceAnalysisService extends BaseService<FinanceAnalysisEntity> {

    /**
     * 获取主要经济指标分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 主要经济指标分析数据
     */
    List<Map<String, Object>> selectMainEconomicIndicators(Map<String, Object> params);

    /**
     * 分页查询财务分析数据
     *
     * @param page            分页参数
     * @param financeAnalysis 查询条件
     * @return 分页数据
     */
    IPage<FinanceAnalysisVO> selectFinanceAnalysisPage(IPage<FinanceAnalysisVO> page, FinanceAnalysisVO financeAnalysis);

    /**
     * 保存财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    boolean saveFinanceAnalysis(FinanceAnalysisEntity financeAnalysis);

    /**
     * 更新财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    boolean updateFinanceAnalysis(FinanceAnalysisEntity financeAnalysis);

    /**
     * 删除财务分析
     *
     * @param ids 主键集合
     * @return 是否成功
     */
    boolean removeFinanceAnalysis(String ids);

    /**
     * 获取财务分析详情
     *
     * @param id 主键
     * @return 财务分析详情
     */
    FinanceAnalysisVO getFinanceAnalysisDetail(Long id);


    /**
     * 根据组合主键查询唯一记录
     *
     * @param name        分析名称
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @return 财务分析记录
     */
    FinanceAnalysisEntity getOneByCompositeKey(String name, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 根据组合主键查询列表
     *
     * @param name        分析名称
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @return 财务分析记录列表
     */
    List<FinanceAnalysisVO> listByCompositeKey(String name, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 分析主要经济指标并保存结果
     *
     * @param queryYear   查询年份
     * @param compareYear 对比年份
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @return 工作流运行ID
     */
    String analyzeMainEconomicIndicators(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 根据workflowRunId查询唯一记录
     *
     * @param workflowRunId   workflowRunId
     * @return 财务分析记录
     */
    FinanceAnalysisEntity getOneByWorkflowRunId(String workflowRunId);

    /**
     * 根据workflowRunId更新执行结果
     *
     * @param workflowRunId   workflowRunId
     * @return 财务分析记录
     */
    FinanceAnalysisVO updateAnalysisResultByWorkflowRunId(String workflowRunId);
}