package org.springblade.modules.yjzb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;

import java.util.List;
import java.util.Map;

/**
 * 财务分析Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FinanceAnalysisMapper extends BaseMapper<FinanceAnalysisEntity> {

    /**
     * 获取主要经济指标分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 主要经济指标分析数据
     */
    List<Map<String, Object>> selectMainEconomicIndicators(@Param("params") Map<String, Object> params);

    /**
     * 获取三项费用分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 三项费用分析数据
     */
    List<Map<String, Object>> selectThreeExpenses(@Param("params") Map<String, Object> params);
    
    /**
     * 分页查询财务分析数据
     *
     * @param page 分页参数
     * @param financeAnalysis 查询条件
     * @return 分页数据
     */
    IPage<FinanceAnalysisVO> selectFinanceAnalysisPage(IPage<FinanceAnalysisVO> page, @Param("financeAnalysis") FinanceAnalysisVO financeAnalysis);
    
    /**
     * 根据组合主键查询唯一记录
     *
     * @param name 分析名称
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 财务分析记录
     */
    FinanceAnalysisEntity selectByCompositeKey(@Param("name") String name, 
                                    @Param("queryYear") Integer queryYear, 
                                    @Param("compareYear") Integer compareYear, 
                                    @Param("startMonth") Integer startMonth, 
                                    @Param("endMonth") Integer endMonth);
}