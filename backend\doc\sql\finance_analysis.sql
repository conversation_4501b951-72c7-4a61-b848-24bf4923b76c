-- 财务分析表
CREATE TABLE IF NOT EXISTS yjzb_finance_analysis (
  id            BIGINT NOT NULL,
  name          VARCHAR(255) DEFAULT NULL,
  type          VARCHAR(50) DEFAULT NULL,
  query_year    INTEGER DEFAULT NULL,
  compare_year  INTEGER DEFAULT NULL,
  start_month   INTEGER DEFAULT NULL,
  end_month     INTEGER DEFAULT NULL,
  input_params  TEXT DEFAULT NULL,
  workflow_run_id VARCHAR(100) DEFAULT NULL,
  execute_time TIMESTAMP(6) DEFAULT now(),
  execute_status VARCHAR(20) DEFAULT NULL,
  result        TEXT DEFAULT NULL,
  sort          INTEGER DEFAULT NULL,
  remark        VARCHAR(255) DEFAULT NULL,
  tenant_id     VARCHAR(12) DEFAULT NULL,
  create_user   BIGINT DEFAULT NULL,
  create_dept   BIGINT DEFAULT NULL,
  create_time   TIMESTAMP(6) WITH TIME ZONE DEFAULT NULL,
  update_user   BIGINT DEFAULT NULL,
  update_time   TIMESTAMP(6) WITH TIME ZONE DEFAULT NULL,
  status        INTEGER DEFAULT NULL,
  is_deleted    INTEGER DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE yjzb_finance_analysis IS '财务分析表';

-- 可选：为字段添加注释（根据你的原始 COMMENT 添加）
COMMENT ON COLUMN yjzb_finance_analysis.tenant_id     IS '租户ID';
COMMENT ON COLUMN yjzb_finance_analysis.create_user   IS '创建人';
COMMENT ON COLUMN yjzb_finance_analysis.create_dept   IS '创建部门';
COMMENT ON COLUMN yjzb_finance_analysis.create_time   IS '创建时间';
COMMENT ON COLUMN yjzb_finance_analysis.update_user   IS '更新人';
COMMENT ON COLUMN yjzb_finance_analysis.update_time   IS '更新时间';
COMMENT ON COLUMN yjzb_finance_analysis.status        IS '状态';
COMMENT ON COLUMN yjzb_finance_analysis.is_deleted    IS '是否已删除';
COMMENT ON COLUMN yjzb_finance_analysis.name          IS '分析名称';
COMMENT ON COLUMN yjzb_finance_analysis.type          IS '分析类型';
COMMENT ON COLUMN yjzb_finance_analysis.query_year    IS '查询年份';
COMMENT ON COLUMN yjzb_finance_analysis.compare_year  IS '对比年份';
COMMENT ON COLUMN yjzb_finance_analysis.start_month   IS '开始月份';
COMMENT ON COLUMN yjzb_finance_analysis.end_month     IS '结束月份';
COMMENT ON COLUMN yjzb_finance_analysis.input_params  IS '输入参数';
COMMENT ON COLUMN yjzb_finance_analysis.workflow_run_id  IS 'workflow_run_id';
COMMENT ON COLUMN yjzb_finance_analysis.execute_time  IS '执行时间';
COMMENT ON COLUMN yjzb_finance_analysis.execute_status  IS '执行状态';
COMMENT ON COLUMN yjzb_finance_analysis.result        IS '分析结果（JSON格式）';
COMMENT ON COLUMN yjzb_finance_analysis.sort          IS '排序';
COMMENT ON COLUMN yjzb_finance_analysis.remark        IS '备注';

