package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.mapper.FinanceAnalysisMapper;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springblade.modules.yjzb.service.IFinanceAnalysisService;
import org.springblade.modules.yjzb.wrapper.FinanceAnalysisWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 财务分析服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceAnalysisServiceImpl extends BaseServiceImpl<FinanceAnalysisMapper, FinanceAnalysisEntity> implements IFinanceAnalysisService {

    private final FinanceAnalysisMapper financeAnalysisMapper;

    private final IDifyService difyService;

    @Value("${dify.api.agentkey.finance-analysis-key:}")
    private String financeAnalysisKey;

    /**
     * 获取主要经济指标分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 主要经济指标分析数据
     */
    @Override
    public List<Map<String, Object>> selectMainEconomicIndicators(Map<String, Object> params) {
        // 参数校验
        if (params == null) {
            params = new HashMap<>();
        }
        
        // 设置默认值
        if (params.get("queryYear") == null) {
            params.put("queryYear", 2025);
        }
        if (params.get("compareYear") == null) {
            params.put("compareYear", 2024);
        }
        if (params.get("startMonth") == null) {
            params.put("startMonth", 1);
        }
        if (params.get("endMonth") == null) {
            params.put("endMonth", 5);
        }
        
        return financeAnalysisMapper.selectMainEconomicIndicators(params);
    }
    
    /**
     * 分页查询财务分析数据
     *
     * @param page 分页参数
     * @param financeAnalysis 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<FinanceAnalysisVO> selectFinanceAnalysisPage(IPage<FinanceAnalysisVO> page, FinanceAnalysisVO financeAnalysis) {
        IPage<FinanceAnalysisVO> pages = financeAnalysisMapper.selectFinanceAnalysisPage(page, financeAnalysis);
        return pages;
    }
    
    /**
     * 保存财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFinanceAnalysis(FinanceAnalysisEntity financeAnalysis) {
        return save(financeAnalysis);
    }
    
    /**
     * 更新财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFinanceAnalysis(FinanceAnalysisEntity financeAnalysis) {
        return updateById(financeAnalysis);
    }
    
    /**
     * 删除财务分析
     *
     * @param ids 主键集合
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeFinanceAnalysis(String ids) {
        return removeByIds(Func.toLongList(ids));
    }
    
    /**
     * 获取财务分析详情
     *
     * @param id 主键
     * @return 财务分析详情
     */
    @Override
    public FinanceAnalysisVO getFinanceAnalysisDetail(Long id) {
        FinanceAnalysisEntity financeAnalysis = getById(id);
        return FinanceAnalysisWrapper.build().entityVO(financeAnalysis);
    }
    
    @Override
    public FinanceAnalysisEntity getOneByCompositeKey(String analysisName, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) {
        LambdaQueryWrapper<FinanceAnalysisEntity> queryWrapper = new LambdaQueryWrapper<FinanceAnalysisEntity>()
            .eq(FinanceAnalysisEntity::getName, analysisName)
            .eq(FinanceAnalysisEntity::getQueryYear, queryYear)
            .eq(compareYear != null, FinanceAnalysisEntity::getCompareYear, compareYear)
            .eq(FinanceAnalysisEntity::getStartMonth, startMonth)
            .eq(FinanceAnalysisEntity::getEndMonth, endMonth);
        
        return getOne(queryWrapper);
    }


    @Override
    public FinanceAnalysisEntity getOneByWorkflowRunId(String workflowRunId) {
        LambdaQueryWrapper<FinanceAnalysisEntity> queryWrapper = new LambdaQueryWrapper<FinanceAnalysisEntity>()
                .eq(FinanceAnalysisEntity::getWorkflowRunId, workflowRunId);
        return getOne(queryWrapper);
    }
    
    @Override
    public List<FinanceAnalysisVO> listByCompositeKey(String name, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) {
        LambdaQueryWrapper<FinanceAnalysisEntity> queryWrapper = new LambdaQueryWrapper<FinanceAnalysisEntity>();

        if (name != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getName, name);
        }
        
        if (queryYear != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getQueryYear, queryYear);
        }
        
        if (compareYear != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getCompareYear, compareYear);
        }
        
        if (startMonth != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getStartMonth, startMonth);
        }
        
        if (endMonth != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getEndMonth, endMonth);
        }
        
        List<FinanceAnalysisEntity> entityList = list(queryWrapper);
        return entityList.stream()
            .map(entity -> FinanceAnalysisWrapper.build().entityVO(entity))
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String analyzeMainEconomicIndicators(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) {
        try {
            // 1. 获取经济指标分析数据
            Map<String, Object> params = new HashMap<>();
            params.put("queryYear", queryYear);
            params.put("compareYear", compareYear);
            params.put("startMonth", startMonth);
            params.put("endMonth", endMonth);
            
            List<Map<String, Object>> indicatorsData = selectMainEconomicIndicators(params);
            
            // 2. 将分析数据作为inputs传递给Dify工作流
            Map<String, Object> inputs = new HashMap<>();
            inputs.put("indicators", indicatorsData);
            inputs.put("queryYear", queryYear);
            inputs.put("compareYear", compareYear);
            inputs.put("startMonth", startMonth);
            inputs.put("endMonth", endMonth);
            String userName = AuthUtil.getUserName();
            // 3. 调用Dify服务启动工作流
            String workflowRunId = difyService.startWorkflowStreaming(inputs, userName, financeAnalysisKey);
            
            if (workflowRunId == null) {
                throw new RuntimeException("启动Dify工作流失败");
            }
            
            log.info("启动主要经济指标AI分析(异步)：queryYear={}, compareYear={}, period={}-{}, workflowRunId={}", 
                    queryYear, compareYear, startMonth, endMonth, workflowRunId);
            
            // 4. 创建或更新财务分析记录
            String analysisName = "主要经济指标";
            FinanceAnalysisEntity entity = new FinanceAnalysisEntity();
            entity.setName(analysisName);
            entity.setType("economic_indicators");
            entity.setQueryYear(queryYear);
            entity.setCompareYear(compareYear);
            entity.setStartMonth(startMonth);
            entity.setEndMonth(endMonth);
            entity.setWorkflowRunId(workflowRunId);
            entity.setResult(Func.toJson(indicatorsData)); // 先保存原始数据
            
            // 5. 根据组合主键查询是否已存在记录
            FinanceAnalysisEntity existingEntity = getOneByCompositeKey(analysisName, queryYear, compareYear, startMonth, endMonth);
            
            boolean success = true;
            if (existingEntity == null) {
                // 新增记录
                success = save(entity);
            } else {
                // 更新记录
                entity.setId(existingEntity.getId());
                success = updateById(entity);
            }
            
            if (!success) {
                throw new RuntimeException("保存财务分析记录失败");
            }
            
            // 6. 异步获取工作流执行详情并更新结果
            CompletableFuture.runAsync(() -> {
                try {
                    // 等待一段时间，确保Dify工作流有足够时间执行
                    Thread.sleep(5000);
                    
                    // 获取工作流执行详情
                    String workflowDetail = difyService.getWorkflowRunDetail(workflowRunId, financeAnalysisKey);
                    
                    if (workflowDetail != null) {
                        entity.setResult(workflowDetail);
                        updateById(entity);
                        log.info("异步更新主要经济指标AI分析结果成功：workflowRunId={}", workflowRunId);
                    } else {
                        log.warn("获取工作流执行详情失败：workflowRunId={}", workflowRunId);
                    }
                } catch (Exception e) {
                    log.error("异步更新主要经济指标AI分析结果失败", e);
                }
            });
            
            // 7. 返回工作流运行ID
            return workflowRunId;
            
        } catch (Exception e) {
            log.error("分析主要经济指标失败", e);
            throw new RuntimeException("分析失败：" + e.getMessage(), e);
        }
    }

    public FinanceAnalysisVO updateAnalysisResultByWorkflowRunId(String workflowRunId) {
        FinanceAnalysisEntity entity = this.getOneByWorkflowRunId(workflowRunId);

        if (null == entity) {
            return null;
        }

        // 如果结果为空或者是原始数据，尝试重新获取工作流执行详情
        if (entity.getResult() == null || entity.getResult().contains("indicators")) {
            String workflowDetail = difyService.getWorkflowRunDetail(workflowRunId, financeAnalysisKey);
            if (workflowDetail != null) {
                entity.setResult(workflowDetail);
                this.updateFinanceAnalysis(entity);
            }
        }

        return FinanceAnalysisWrapper.build().entityVO(entity);
    }
}