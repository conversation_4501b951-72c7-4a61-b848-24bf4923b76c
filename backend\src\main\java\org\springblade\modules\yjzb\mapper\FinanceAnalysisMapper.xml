<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.FinanceAnalysisMapper">

    <!-- 财务分析结果映射 -->
    <resultMap id="financeAnalysisResultMap" type="org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="query_year" property="queryYear"/>
        <result column="compare_year" property="compareYear"/>
        <result column="start_month" property="startMonth"/>
        <result column="end_month" property="endMonth"/>
        <result column="input_params" property="inputParams"/>
        <result column="workflow_run_id" property="workflowRunId"/>
        <result column="execute_time" property="executeTime"/>
        <result column="execute_status" property="executeStatus"/>
        <result column="result" property="result"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 分页查询财务分析数据 -->
    <select id="selectFinanceAnalysisPage" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO">
        SELECT * FROM yjzb_finance_analysis
        <where>
            <if test="financeAnalysis.name != null and financeAnalysis.name != ''">
                AND name LIKE CONCAT('%', #{financeAnalysis.name}, '%')
            </if>
            <if test="financeAnalysis.type != null and financeAnalysis.type != ''">
                AND type = #{financeAnalysis.type}
            </if>
            <if test="financeAnalysis.queryYear != null">
                AND year = #{financeAnalysis.queryYear}
            </if>
            <if test="financeAnalysis.compareYear != null">
                AND compare_year = #{financeAnalysis.compareYear}
            </if>
            <if test="financeAnalysis.startMonth != null">
                AND start_month = #{financeAnalysis.startMonth}
            </if>
            <if test="financeAnalysis.endMonth != null">
                AND end_month = #{financeAnalysis.endMonth}
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>
    
    <!-- 根据组合主键查询唯一记录 -->
    <select id="selectByCompositeKey" resultMap="financeAnalysisResultMap">
        SELECT * FROM yjzb_finance_analysis
        <where>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="queryYear != null">
                AND query_year = #{queryYear}
            </if>
            <if test="compareYear != null">
                AND compare_year = #{compareYear}
            </if>
            <if test="startMonth != null">
                AND start_month = #{startMonth}
            </if>
            <if test="endMonth != null">
                AND end_month = #{endMonth}
            </if>
        </where>
        LIMIT 1
    </select>

    <!-- 主要经济指标分析 -->
    <select id="selectMainEconomicIndicators" resultType="java.util.HashMap">
        WITH base_data AS (
            SELECT 
                i.code,
                i.name AS indicator_name,
                EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
                ROUND(SUM(iv.value) / 10000, 2) AS actual_value,
                ROUND(SUM(iv.value) / 10000, 2) AS budget_value
            FROM yjzb_indicator i
            JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
            WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (#{params.compareYear}, #{params.queryYear})
              AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN #{params.startMonth} AND #{params.endMonth}
            GROUP BY i.code, i.name, EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM'))
        ),

        cigarette_sales_data AS (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM')) AS year,
                SUM(sales) AS total_sales
            FROM yjzb_cigarette_sales
            WHERE EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM')) IN (#{params.compareYear}, #{params.queryYear})
              AND EXTRACT(MONTH FROM TO_DATE(period, 'YYYY-MM')) BETWEEN #{params.startMonth} AND #{params.endMonth}
              AND is_deleted = 0
            GROUP BY EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM'))
        ),

        base_pivot_data AS (
            SELECT
                code,
                indicator_name,
                MAX(CASE WHEN year = #{params.queryYear} THEN actual_value END) AS v_query_actual,
                MAX(CASE WHEN year = #{params.compareYear} THEN actual_value END) AS v_compare_actual,
                MAX(CASE WHEN year = #{params.queryYear} THEN budget_value END) AS v_query_budget
            FROM base_data
            GROUP BY code, indicator_name
        ),

        pivot_data AS (
            SELECT * FROM base_pivot_data

            UNION ALL

            SELECT
                'juanyan_xiaoshou_shuliang' AS code,
                '卷烟销售数量' AS indicator_name,
                COALESCE((SELECT total_sales FROM cigarette_sales_data WHERE year = 2025), 0) AS v_2025_actual,
                COALESCE((SELECT total_sales FROM cigarette_sales_data WHERE year = 2024), 0) AS v_2024_actual,
                0 AS v_2025_budget
        ),

        calculated AS (
            -- 税利总额
            SELECT
                '税利总额' AS item,
                COALESCE(p.v_query_actual, 0) AS v_query_actual,
                COALESCE(p.v_compare_actual, 0) AS v_compare_actual,
                COALESCE(p.v_query_budget, 0) AS v_query_budget
            FROM pivot_data p
            WHERE p.indicator_name = '税利合计' OR p.code = 'shuilixiangji'

            UNION ALL

            -- 其中：利润总额
            SELECT
                '其中：利润总额',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.code = 'lirun_zonge_shui'

            UNION ALL

            -- 税费（不含所得税） = 税利合计 - 利润总额
            SELECT
                '税费（不含所得税）',
                COALESCE(p1.v_query_actual, 0) - COALESCE(p2.v_query_actual, 0),
                COALESCE(p1.v_compare_actual, 0) - COALESCE(p2.v_compare_actual, 0),
                COALESCE(p1.v_query_budget, 0) - COALESCE(p2.v_query_budget, 0)
            FROM 
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '税利合计' OR code = 'shuilixiangji') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE code = 'lirun_zonge_shui') p2

            UNION ALL

            -- 卷烟销售数量
            SELECT
                '卷烟销售数量',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '卷烟销售数量' OR p.code = 'juanyan_xiaoshou_shuliang'

            UNION ALL

            -- 卷烟销售收入
            SELECT
                '卷烟销售收入',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '主营业务收入' OR p.code = 'zhuying_yewu_shouru'

            UNION ALL

            -- 卷烟销售成本
            SELECT
                '卷烟销售成本',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '主营业务成本' OR p.code = 'zhuying_yewu_chengben'

            UNION ALL

            -- 毛利额 = 收入 - 成本
            SELECT
                '毛利额',
                COALESCE(p1.v_query_actual, 0) - COALESCE(p2.v_query_actual, 0),
                COALESCE(p1.v_compare_actual, 0) - COALESCE(p2.v_compare_actual, 0),
                COALESCE(p1.v_query_budget, 0) - COALESCE(p2.v_query_budget, 0)
            FROM 
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务收入' OR code = 'zhuying_yewu_shouru') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务成本' OR code = 'zhuying_yewu_chengben') p2

            UNION ALL

            -- 其他业务收入
            SELECT
                '其他业务收入',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '其他业务收入' OR p.code = 'qita_yewu_shouru'

            UNION ALL

            -- 其他业务成本
            SELECT
                '其他业务成本',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '其他业务成本' OR p.code = 'qita_yewu_chengben'

            UNION ALL

            -- 三项费用总额
            SELECT
                '三项费用总额',
                COALESCE(s.v_query_actual, 0) + COALESCE(m.v_query_actual, 0) + COALESCE(r.v_query_actual, 0) + COALESCE(f.v_query_actual, 0),
                COALESCE(s.v_compare_actual, 0) + COALESCE(m.v_compare_actual, 0) + COALESCE(r.v_compare_actual, 0) + COALESCE(f.v_compare_actual, 0),
                COALESCE(s.v_query_budget, 0) + COALESCE(m.v_query_budget, 0) + COALESCE(r.v_query_budget, 0) + COALESCE(f.v_query_budget, 0)
            FROM
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '销售费用') s,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '管理费用') m,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '研发费用') r,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '财务费用') f
        )

        SELECT
            item AS "项目",
            TO_CHAR(ROUND(v_query_actual, 2), 'FM999,999,990.00') AS "${params.queryYear}年${params.startMonth}-${params.endMonth}月",
            TO_CHAR(ROUND(v_compare_actual, 2), 'FM999,999,990.00') AS "${params.compareYear}年${params.startMonth}-${params.endMonth}月",
            CASE 
                WHEN v_compare_actual = 0 THEN 'n/a'
                ELSE TO_CHAR(ROUND((v_query_actual - v_compare_actual) / NULLIF(v_compare_actual, 0) * 100, 2), 'FM990.00')
            END AS "同比增减（%）",
            TO_CHAR(ROUND(v_query_budget, 2), 'FM999,999,990.00') AS "${params.queryYear}年预算数",
            CASE 
                WHEN v_query_budget = 0 THEN ''
                ELSE TO_CHAR(ROUND(v_query_actual / NULLIF(v_query_budget, 0) * 100, 2), 'FM990.00')
            END AS "预算执行进度（%）"
        FROM (
            SELECT * FROM calculated

            UNION ALL

            -- 毛利率（%）
            SELECT
                '毛利率（%）' AS item,
                ROUND((COALESCE(p1.v_query_actual, 0) - COALESCE(p2.v_query_actual, 0)) / NULLIF(COALESCE(p1.v_query_actual, 0), 0) * 100, 2),
                ROUND((COALESCE(p1.v_compare_actual, 0) - COALESCE(p2.v_compare_actual, 0)) / NULLIF(COALESCE(p1.v_compare_actual, 0), 0) * 100, 2),
                ROUND((COALESCE(p1.v_query_budget, 0) - COALESCE(p2.v_query_budget, 0)) / NULLIF(COALESCE(p1.v_query_budget, 0), 0) * 100, 2)
            FROM 
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务收入') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务成本') p2

            UNION ALL

            -- 三项费用率（%）
            SELECT
                '三项费用率（%）' AS item,
                ROUND((COALESCE(s.v_query_actual, 0) + COALESCE(m.v_query_actual, 0) + COALESCE(r.v_query_actual, 0) + COALESCE(f.v_query_actual, 0)) / NULLIF(p1.v_query_actual, 0) * 100, 2),
                ROUND((COALESCE(s.v_compare_actual, 0) + COALESCE(m.v_compare_actual, 0) + COALESCE(r.v_compare_actual, 0) + COALESCE(f.v_compare_actual, 0)) / NULLIF(p1.v_compare_actual, 0) * 100, 2),
                ROUND((COALESCE(s.v_query_budget, 0) + COALESCE(m.v_query_budget, 0) + COALESCE(r.v_query_budget, 0) + COALESCE(f.v_query_budget, 0)) / NULLIF(p1.v_query_budget, 0) * 100, 2)
            FROM
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务收入') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '销售费用') s,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '管理费用') m,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '研发费用') r,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '财务费用') f
        ) t
        ORDER BY 
            CASE item
                WHEN '税利总额' THEN 1
                WHEN '其中：利润总额' THEN 2
                WHEN '税费（不含所得税）' THEN 3
                WHEN '卷烟销售数量' THEN 4
                WHEN '卷烟销售收入' THEN 5
                WHEN '卷烟销售成本' THEN 6
                WHEN '毛利额' THEN 7
                WHEN '毛利率（%）' THEN 8
                WHEN '其他业务收入' THEN 9
                WHEN '其他业务成本' THEN 10
                WHEN '三项费用总额' THEN 11
                WHEN '三项费用率（%）' THEN 12
            END
    </select>

</mapper>