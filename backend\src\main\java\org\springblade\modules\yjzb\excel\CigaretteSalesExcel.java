/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.io.Serializable;

/**
 * 卷烟销量数据导出模型
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
public class CigaretteSalesExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 年份
	 */
	@ExcelProperty("年份")
	private Integer year;

	/**
	 * 月份
	 */
	@ExcelProperty("月份")
	private Integer month;

	/**
	 * 销量（箱）
	 */
	@ExcelProperty("销量（箱）")
	private Integer sales;

	/**
	 * 备注
	 */
	@ExcelProperty("备注")
	private String remark;

}